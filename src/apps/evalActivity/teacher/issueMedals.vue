<template>
    <view class="issue_medals">
        <z-paging ref="paging" v-model="state.medalList" @query="queryList" :auto="false">
            <template #top>
                <uni-nav-bar :showMenuButtonWidth="true" statusBar fixed left-icon="left" :border="false" @clickLeft="clickLeft" title="发放勋章" :leftWidth="navBarLeftWidth()" :rightWidth="navBarRightWidth()"> </uni-nav-bar>
            </template>
            <!-- 选择学生的页面 -->
            <view v-if="activeStep == 1" class="select_student">
                <uni-search-bar v-model="searchKeyword" style="background-color: #ffffff; margin-bottom: 20rpx" bgColor="#F0F2F5" radius="30" placeholder="搜索姓名" clearButton="auto" cancelButton="none" @confirm="search" @clear="studentClear" />
                <view class="select_student_list_box">
                    <view class="select_student_item_title_box" v-for="item in state.allList" :key="item.classesId">
                        <view class="select_student_item_title">
                            <view class="select_student_item_title_text" @click="toggleClassExpand(item)">
                                <text>{{ item.classesName }}</text>
                                <uni-icons class="select_student_item_title_icon" :type="item.isExpanded ? 'up' : 'down'" size="20" color="#999"></uni-icons>
                            </view>
                            <view class="select_student_item_title_btn" v-if="item.studentList.length > 0">
                                <image @click="changeAllNoSelect(item)" v-if="allSelect(item)" class="yes_select_img" src="https://file.1d1j.cn/cloud-mobile/evalActivity/yesSelect.png" alt="" />
                                <image @click="changeAllSelect(item)" v-else class="no_select_img" src="https://file.1d1j.cn/cloud-mobile/evalActivity/noSelect.png" alt="" />
                                <text class="select_student_item_title_btn_text">全选</text>
                            </view>
                        </view>
                        <view v-if="item.isExpanded">
                            <view v-if="item.isLoading" class="select_student_list_loading">
                                <uv-loading-icon text="加载中" :vertical="true" color="#999" size="24"></uv-loading-icon>
                            </view>
                            <template v-else>
                                <view v-if="item.studentList.length > 0" class="select_student_list">
                                    <view class="select_student_item" v-for="student in item.studentList" :key="student.id" @click="changeStudent(student)">
                                        <view class="select_student_item_select">
                                            <image mode="aspectFill" class="select_student_item_avatar_img" v-if="student.isSelect" src="https://file.1d1j.cn/cloud-mobile/evalActivity/yesSelect.png" alt="" />
                                            <image mode="aspectFill" class="select_student_item_avatar_img" v-else src="https://file.1d1j.cn/cloud-mobile/evalActivity/noSelect.png" alt="" />
                                        </view>

                                        <view class="select_student_item_avatar">
                                            <image mode="aspectFill" class="select_student_item_avatar_img" v-if="!true" src="https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png" alt="" />
                                            <view class="select_student_item_avatar_text" v-else>{{ student.name[0] }}</view>
                                        </view>
                                        <view class="select_student_item_num">
                                            <image class="select_student_item_num_img" mode="scaleToFill" v-if="true" src="https://file.1d1j.cn/cloud-mobile/evalActivity/xunzhangIcon.png" />
                                            <text class="select_student_item_num_text">{{ student.medalCount }}</text>
                                        </view>
                                        <view class="select_student_item_name">{{ student.name }}</view>
                                    </view>
                                </view>
                                <view v-else class="select_student_list_empty">暂无学生</view>
                            </template>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 选择勋章的页面 -->
            <view v-if="activeStep == 2" class="handler_content">
                <view class="name_list_box" @click="showSelectedStudents">
                    <view class="name_list_box_item"> 发放给: </view>
                    <view class="name_list_box_item">
                        <text class="name_list_text">{{ selectedNamesPreview }}</text>
                        <text> <uni-icons type="right" size="20" color="#333"></uni-icons> </text>
                    </view>
                </view>
                <view class="con_list_box">
                    <uv-tabs :list="medalTypeList" lineWidth="20" lineColor="var(--primary-color)" :current="tabsCurrent" :activeStyle="{ color: 'var(--primary-color)' }" :inactiveStyle="{ color: '#999999' }" :customStyle="{ background: '#fff' }" @click="tabsClick"></uv-tabs>
                    <checkbox-group class="reset_radio_group" @change="radioChange">
                        <label class="medal_col" v-for="item in state.medalList" :key="item.id">
                            <view class="radio" :class="{ active: state.medalCodeList.includes(item.medalCode) }">
                                <checkbox :value="item.medalCode" :checked="state.medalCodeList.includes(item.medalCode)" />
                            </view>
                            <image mode="aspectFill" class="issue_medals_img" :src="item.medalIconUrl" alt="" />
                        </label>
                    </checkbox-group>
                </view>
            </view>

            <template #bottom>
                <view class="foote_btn_step1" v-if="activeStep == 1">
                    <text @click="showSelectedStudents" class="selected_count">已选 {{ selectedStudentCount }} 人</text>
                    <view class="next_step_btn" @click="nextStep"> 确定 </view>
                </view>
                <view class="foote_btn" v-if="activeStep == 2">
                    <button type="primary" :loading="state.loading" @click="submitMedal">确定发放</button>
                </view>
            </template>
            <template #empty>
                <yd-empty text="暂无数据" />
            </template>
        </z-paging>
        <!-- 已选学生详情 -->
        <uni-popup ref="popupRef" type="bottom" backgroundColor="#fff" borderRadius="10px 10px 0px 0px">
            <view class="popup_container">
                <view class="popup_title">
                    <text></text>
                    <text>详情</text>
                    <uni-icons type="closeempty" size="20" @click="popupRef.close()"></uni-icons>
                </view>
                <view class="popup_selected_summary">
                    <text class="popup_selected_summary_label">已选：</text>
                    <text class="popup_selected_summary_value">{{ selectedStudentCount }}人</text>
                </view>
                <scroll-view scroll-y class="popup_list_scroll">
                    <view v-if="selectedClassList.length > 0">
                        <view class="popup_class_item" v-for="classes in selectedClassList" :key="classes.classesId">
                            <text class="popup_class_title">{{ classes.classesName }}({{ classes.count }}人)</text>
                            <text class="popup_class_students">{{ classes.names.join("，") }}</text>
                        </view>
                    </view>
                    <view v-else class="popup_empty">
                        <yd-empty text="暂无已选学生" />
                    </view>
                </scroll-view>
            </view>
        </uni-popup>
    </view>
</template>

<script setup>
import { onLoad } from "@dcloudio/uni-app"

const popupRef = ref(null)
const paging = ref(null)
const evalTypeId = shallowRef("")
const medalTypeList = ref([])
const tabsCurrent = ref(0) // tabs当前索引
const medalTypeId = ref(null) // tabs当前id

const activeStep = ref(1) // 当前步骤 1.选择学生 2.选择勋章

const searchKeyword = ref("")

const state = reactive({
    medalCodeList: [],
    personListDTO: [],
    medalList: [],
    loading: false,
    classesId: "",
    allList: []
})

// 记录全量班级及学生数据，便于搜索后恢复
const originClassList = shallowRef(state.all)

/**
 * 在当前展示列表及原始列表中同步班级信息
 * @param {string} classesId - 班级ID
 * @param {Record<string, any>} patch - 需要同步的字段
 */
const patchClassItem = (classesId, patch = {}) => {
    if (!classesId || !patch || typeof patch !== "object") return
    const applyPatch = (list) => {
        if (!Array.isArray(list)) return
        const target = list.find((classes) => classes.classesId === classesId)
        if (target) Object.assign(target, patch)
    }
    applyPatch(state.allList)
    if (originClassList.value !== state.allList) {
        applyPatch(originClassList.value)
    }
}

/**
 * 根据班级ID获取当前展示或原始列表中的班级对象
 * @param {string} classesId - 班级ID
 * @returns {Record<string, any> | null}
 */
const findClassItem = (classesId) => {
    if (!classesId) return null
    const current = Array.isArray(state.allList) ? state.allList.find((classes) => classes.classesId === classesId) : null
    if (current) return current
    if (Array.isArray(originClassList.value)) {
        return originClassList.value.find((classes) => classes.classesId === classesId) ?? null
    }
    return null
}

/**
 * 选中学生的班级列表
 */
const selectedClassList = computed(() => {
    const source = originClassList.value ?? []
    return source
        .map((classes) => {
            const selectedStudents = (classes.studentList ?? []).filter((student) => student.isSelect)
            return selectedStudents.length
                ? {
                      classesId: classes.classesId,
                      classesName: classes.classesName,
                      count: selectedStudents.length,
                      names: selectedStudents.map((student) => student.name ?? "").filter((name) => !!name)
                  }
                : null
        })
        .filter(Boolean)
})

/**
 * 选中学生总数
 */
const selectedStudentCount = computed(() => selectedClassList.value.reduce((sum, item) => sum + item.count, 0))

/**
 * 选中学生名称预览，顿号分隔并用于第二步顶部展示
 */
const selectedNamesPreview = computed(() => {
    const nameList = selectedClassList.value.flatMap((item) => item.names)
    return nameList.length ? nameList.join("、") : "请选择学生"
})

const normalizeSearchValue = (payload) => {
    if (typeof payload?.value === "string") return payload.value.trim()
    return ""
}

const search = (payload) => {
    const keyword = normalizeSearchValue(payload)
    if (!keyword) {
        studentClear()
        return
    }

    // 显示加载状态
    uni.showLoading({
        title: "搜索中..."
    })

    http.post("/app/student/search", {
        name: keyword,
        queryMedal: true
    })
    .then(({ data }) => {
        // 处理搜索结果数据
        handleSearchResult(data || [])
    })
    .catch((error) => {
        console.error("搜索失败:", error)
        uni.showToast({
            title: "搜索失败，请重试",
            icon: "none"
        })
        // 搜索失败时恢复原始列表
        studentClear()
    })
    .finally(() => {
        uni.hideLoading()
    })
}

/**
 * 处理搜索结果数据
 * @param {Array} searchData - 搜索返回的学生数据
 */
const handleSearchResult = (searchData) => {
    if (!Array.isArray(searchData) || searchData.length === 0) {
        // 没有搜索结果时显示空列表
        state.allList = []
        uni.showToast({
            title: "未找到相关学生",
            icon: "none"
        })
        return
    }

    // 按班级分组搜索结果
    const classMap = new Map()

    searchData.forEach(student => {
        const { classesId, className } = student
        if (!classMap.has(classesId)) {
            classMap.set(classesId, {
                classesId,
                classesName: className,
                studentList: [],
                isExpanded: true, // 搜索结果默认展开
                isLoading: false,
                hasLoadedStudents: true
            })
        }

        // 检查学生是否已被选中（从原始数据中查找）
        const originalStudent = findStudentInOriginalData(student.id)
        const isSelect = originalStudent ? originalStudent.isSelect : false

        classMap.get(classesId).studentList.push({
            ...student,
            isSelect,
            medalCount: student.medalCount || 0
        })
    })

    // 转换为数组并更新状态
    state.allList = Array.from(classMap.values())
}

/**
 * 在原始数据中查找学生
 * @param {string} studentId - 学生ID
 * @returns {Object|null} 学生对象或null
 */
const findStudentInOriginalData = (studentId) => {
    if (!Array.isArray(originClassList.value)) return null

    for (const classItem of originClassList.value) {
        if (Array.isArray(classItem.studentList)) {
            const student = classItem.studentList.find(s => s.id === studentId)
            if (student) return student
        }
    }
    return null
}

const studentClear = () => {
    state.allList = originClassList.value ?? []
    if (searchKeyword.value) {
        searchKeyword.value = ""
    }
}

/**
 * 打开已选学生弹窗
 */
const showSelectedStudents = () => {
    if (!selectedStudentCount.value) {
        uni.showToast({
            title: "请先选择学生",
            icon: "none"
        })
        return
    }
    popupRef.value?.open()
}

const radioChange = (e) => {
    state.medalCodeList = e.detail.value
}

// 调用List数据
function queryList(pageNo, pageSize) {
    const params = {
        evalTypeId: evalTypeId.value,
        medalTypeId: medalTypeId.value,
        issuanceMethod: 2,
        medalStatus: 1,
        pageNo,
        pageSize
    }
    http.post("/app/appEvalMedal/pageEvalMedal", params).then(({ data }) => {
        paging.value.complete(data?.list)
    })
}

function clickLeft() {
    if (activeStep.value == 1) {
        uni.navigateBack()
    }
    if (activeStep.value == 2) {
        activeStep.value = 1
    }
}

// 提交发放勋章
const submitMedal = () => {
    const { medalCodeList } = state
    const params = { medalCodeList }
    params.personListDTO = state.allList
        .flatMap((item) => item.studentList)
        .filter((item) => item.isSelect)
        .map((obj) => {
            return {
                id: obj.id,
                identity: 0,
                typeValue: "student",
                name: obj.name
            }
        })
    state.loading = true
    http.post("/app/appEvalMedal/person/issuanceMedal", params)
        .then(({ message }) => {
            uni.navigateBack()
            uni.showToast({
                title: message
            })
        })
        .finally(() => {
            state.loading = false
        })
}

// tabs切换点击
function tabsClick(item) {
    tabsCurrent.value = item.index
    medalTypeId.value = item.id
    paging.value?.reload()
}

async function getMedalType() {
    await http.post("/app/evalMedalType/listEvalMedalType", {}).then((res) => {
        medalTypeList.value = [{ id: null, name: "全部勋章" }, ...res.data]
        tabsCurrent.value = 0
        medalTypeId.value = medalTypeList.value[0]?.id
    })
}

const nextStep = () => {
    if (!!selectedStudentCount.value) {
        activeStep.value = 2
    } else {
        uni.showToast({ title: "请选择学生", icon: "none" })
    }
}

const changeStudent = (item) => {
    item.isSelect = !item.isSelect

    // 同步选中状态到原始数据中
    syncStudentSelectStatus(item.id, item.isSelect)
}

/**
 * 同步学生选中状态到原始数据
 * @param {string} studentId - 学生ID
 * @param {boolean} isSelect - 选中状态
 */
const syncStudentSelectStatus = (studentId, isSelect) => {
    if (!Array.isArray(originClassList.value)) return

    // 首先尝试在已加载的学生数据中查找
    for (const classItem of originClassList.value) {
        if (Array.isArray(classItem.studentList)) {
            const student = classItem.studentList.find(s => s.id === studentId)
            if (student) {
                student.isSelect = isSelect
                return // 找到并更新后直接返回
            }
        }
    }

    // 如果在已加载的数据中找不到，说明是通过搜索选择的学生
    // 需要从当前显示的搜索结果中获取学生信息并添加到原始数据中
    const searchStudent = findStudentInCurrentList(studentId)
    if (searchStudent) {
        addStudentToOriginalData(searchStudent, isSelect)
    }
}

/**
 * 在当前显示列表中查找学生
 * @param {string} studentId - 学生ID
 * @returns {Object|null} 学生对象或null
 */
const findStudentInCurrentList = (studentId) => {
    if (!Array.isArray(state.allList)) return null

    for (const classItem of state.allList) {
        if (Array.isArray(classItem.studentList)) {
            const student = classItem.studentList.find(s => s.id === studentId)
            if (student) return student
        }
    }
    return null
}

/**
 * 将学生添加到原始数据中
 * @param {Object} student - 学生对象
 * @param {boolean} isSelect - 选中状态
 */
const addStudentToOriginalData = (student, isSelect) => {
    if (!Array.isArray(originClassList.value)) return

    // 查找对应的班级
    const targetClass = originClassList.value.find(classItem => classItem.classesId === student.classesId)
    if (targetClass) {
        // 确保 studentList 是数组
        if (!Array.isArray(targetClass.studentList)) {
            targetClass.studentList = []
        }

        // 检查学生是否已存在
        const existingStudent = targetClass.studentList.find(s => s.id === student.id)
        if (!existingStudent) {
            // 添加新学生到班级中
            targetClass.studentList.push({
                ...student,
                isSelect
            })
        } else {
            // 更新现有学生的选中状态
            existingStudent.isSelect = isSelect
        }
    }
}

// 判断是否全选
const allSelect = (data) => {
    return data.studentList.length > 0 && data.studentList.every((item) => item.isSelect)
}

// 全部选中
const changeAllSelect = (data) => {
    data.studentList.forEach((item) => {
        item.isSelect = true
        // 同步到原始数据
        syncStudentSelectStatus(item.id, true)
    })
}

// 全部取消选中
const changeAllNoSelect = (data) => {
    data.studentList.forEach((item) => {
        item.isSelect = false
        // 同步到原始数据
        syncStudentSelectStatus(item.id, false)
    })
}

/**
 * 切换班级展开状态并在首次展开时加载学生数据
 * @param {Record<string, any>} classesItem - 当前班级
 */
const toggleClassExpand = async (classesItem) => {
    const classesId = classesItem?.classesId
    if (!classesId) return
    const nextExpanded = !classesItem.isExpanded
    patchClassItem(classesId, { isExpanded: nextExpanded })

    if (!nextExpanded) return

    const current = findClassItem(classesId)
    if (current?.isLoading || current?.hasLoadedStudents) {
        return
    }

    patchClassItem(classesId, { isLoading: true })
    try {
        const studentList = await getStudentList(classesId)
        patchClassItem(classesId, {
            studentList,
            hasLoadedStudents: true,
            isLoading: false
        })
    } catch (error) {
        patchClassItem(classesId, { isLoading: false, isExpanded: false })
        uni.showToast({
            title: error?.message || "获取学生失败",
            icon: "none"
        })
    }
}

// 获取学生列表
const normalizeStudentResponse = (data) => {
    if (Array.isArray(data)) return data
    if (Array.isArray(data?.list)) return data.list
    if (Array.isArray(data?.rows)) return data.rows
    if (Array.isArray(data?.records)) return data.records
    return []
}

/**
 * 根据班级ID获取学生列表
 * @param {string} classesId - 班级ID
 * @returns {Promise<Array>} 学生数组
 */
const getStudentList = async (classesId) => {
    const { data } = await http.post("/app/student/allStudents", {
        id: classesId,
        queryMedal: true,
        type: 4,
        pageNo: 1,
        pageSize: 500
    })
    const studentList = normalizeStudentResponse(data)
    return studentList.map((student) => ({
        ...student,
        isSelect: Boolean(student.isSelect)
    }))
}

/**
 * 提取所有班级（children）到一个新数组
 * @param {Array} data - 原始年级数组
 * @returns {Array} 所有班级的扁平数组
 */
function extractAllClasses(data) {
    return (data ?? []).reduce((acc, grade) => {
        // 兼容没有 children 的情况
        return acc.concat(grade.children || [])
    }, [])
}

const getClassList = async () => {
    const { data } = await http.get("/app/timetable/getClassList", {})
    const allClasses = extractAllClasses(data)

    state.allList = allClasses.map((item) => {
        return {
            classesId: item.id,
            classesName: item.showName,
            studentList: [],
            isExpanded: false,
            isLoading: false,
            hasLoadedStudents: false
        }
    })
    originClassList.value = state.allList
}

onLoad(async (options) => {
    Object.keys(options).forEach((key) => {
        options[key] = decodeURIComponent(options[key])
    })
    evalTypeId.value = options?.issue_medals
    await getClassList()
    await getMedalType()

    nextTick(() => {
        paging.value?.reload()
    })
})
</script>

<style lang="scss" scoped>
.issue_medals {
    background: $uni-bg-color-grey;
    min-height: calc(100vh - 88rpx);
}

.handler_content {
    overflow: hidden auto;

    .reset_radio_group {
        margin-top: 30rpx;
        overflow: hidden;

        .medal_col {
            width: 50%;
            float: left;
            box-sizing: border-box;
            text-align: center;

            .radio {
                text-align: right;
                padding-right: 40rpx;

                &.active {
                    :deep(.uni-checkbox-input) {
                        background-color: var(--primary-color) !important;
                        border-color: var(--primary-color) !important;

                        svg {
                            color: $uni-bg-color;

                            path {
                                fill: $uni-bg-color;
                            }
                        }
                    }
                }

                :deep(.uni-checkbox-input) {
                    border-radius: 50%;
                }
            }

            .issue_medals_img {
                width: 220rpx;
                height: 224rpx;
            }
        }
    }
}

.foote_btn {
    padding: 30rpx;
    background-color: $uni-bg-color;
    button {
        background-color: var(--primary-color);
    }
}

.foote_btn_step1 {
    border-top: 1px solid #d9d9d9;
    padding: 30rpx;
    background-color: $uni-bg-color;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .selected_count {
        font-weight: 400;
        font-size: 28rpx;
        color: #666666;
        line-height: 40rpx;
    }
    .next_step_btn {
        width: 300rpx;
        height: 80rpx;
        background-color: var(--primary-color);
        text-align: center;
        border-radius: 10rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: $uni-bg-color;
        line-height: 80rpx;
    }
}

.select_student_item {
    position: relative;
}

.select_student_item_avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;

    .select_student_item_avatar_img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }
    .select_student_item_avatar_text {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100rpx;
        height: 100rpx;
        border-radius: 50%;
        background: #00b781;
        font-weight: 600;
        font-size: 47rpx;
        color: #ffffff;
    }
}

.select_student_item_num {
    transform: translateY(-10px);
    width: 100rpx;
    background: #fef6db;
    border-radius: 14rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .select_student_item_num_img {
        width: 20rpx;
        height: 22rpx;
    }
    .select_student_item_num_text {
        font-weight: 400;
        font-size: 24rpx;
        color: #f7a33b;
    }
}

.select_student_item_name {
    transform: translateY(-7px);
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    width: 140rpx;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.select_student_list {
    background-color: $uni-bg-color;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20rpx;
    padding: 30rpx;
    .select_student_item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
}

.select_student_item_select {
    position: absolute;
    width: 36rpx;
    height: 36rpx;
    border: 1px solid #ffffff;
    border-radius: 50%;
    position: absolute;
    right: 16rpx;
    top: -10rpx;
    .select_student_item_avatar_img {
        width: 100%;
        height: 100%;
    }
}

.popup_container {
    height: 1000rpx;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    .popup_title {
        font-weight: 500;
        font-size: 34rpx;
        color: #333333;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .popup_selected_summary {
        margin-top: 20rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        .popup_selected_summary_value {
            color: #00b781;
            font-weight: 600;
        }
    }
    .popup_list_scroll {
        margin-top: 24rpx;
        flex: 1;
        width: 100%;
    }
    .popup_class_item {
        margin-bottom: 32rpx;
        .popup_class_title {
            font-weight: 600;
            font-size: 28rpx;
            color: #333333;
        }
        .popup_class_students {
            display: block;
            margin-top: 12rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #666666;
            line-height: 40rpx;
        }
    }
    .popup_empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.select_student_item_info {
    position: absolute;
    bottom: -30px;
}

.select_student_list_box {
    .select_student_item_title_box {
        padding-bottom: 20rpx;
    }
    .select_student_item_title {
        background-color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        .select_student_item_title_text {
            font-weight: 600;
            font-size: 30rpx;
            color: #333333;
            display: flex;
            align-items: center;
            gap: 12rpx;
            flex: 1;
            cursor: pointer;
        }
        .select_student_item_title_icon {
            flex-shrink: 0;
            transition: transform 0.2s ease;
        }
    }
}

.select_student_item_title_btn {
    display: flex;
    align-items: center;
    justify-content: center;
    .select_student_item_title_btn_text {
        font-weight: 400;
        font-size: 30rpx;
        color: #333333;
    }
}
.yes_select_img,
.no_select_img {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
}

.select_student_list_loading {
    padding: 40rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.select_student_list_empty {
    padding: 40rpx;
    text-align: center;
    font-size: 26rpx;
    color: #999999;
}

.name_list_box {
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 30rpx 30rpx 30rpx;
    margin-bottom: 20rpx;
    .name_list_box_item {
        font-size: 28rpx;
        color: #333333;
        display: flex;
        align-items: center;
    }
    .name_list_text {
        max-width: 500rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}
.con_list_box {
    background-color: #ffffff;
}
</style>
