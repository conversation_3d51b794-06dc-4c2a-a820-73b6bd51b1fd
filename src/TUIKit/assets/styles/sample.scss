/* stylelint-disable */
.TUIKit {
  display: flex;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  text-align: left;
  .TUIKit-navbar {
    background: #e8e8e9;
    overflow: hidden;
    .TUIKit-navbar-item {
      padding: 10px;
      color: #147aff;
      font-weight: 500;
      cursor: pointer;
    }
    .TUIKit-navbar-item-active {
      background: #dddddd;
    }
  }
  .TUIKit-main-container {
    flex: 1;
    display: flex;
    overflow: hidden;
    .TUIKit-main {
      flex: 1;
      display: flex;
      .TUIKit-main-aside {
        min-width: 285px;
        flex: 0 0 24%;
        border-right: 1px solid #f4f5f9;
      }
      .TUIKit-main-main {
        flex: 1;
        display: flex;
        .chat{
          flex: 1;
        }
        .chat-aside {
          position: absolute;
          top: 50px;
          right: 0;
          box-sizing: border-box;
          max-width: 360px;
          max-height: calc(100% - 50px);
          border-radius: 8px 0 0 8px;
          z-index: 9999;
        }
      }
    }
    .callkit-container {
      position: fixed;
      left: calc(50% - 25rem);
      top: calc(50% - 18rem);
      width: 50rem;
      height: 36rem;
    }
    .callkit-container.miniMized {
      left: auto;
      right: 10px;
      top: 70px;
      background: transparent;
    }
  }
}
.TUIKit-h5 {
  display: flex;
  flex-direction: column-reverse;
  .TUIKit-navbar {
    display: flex;
    flex-direction: row;
    .TUIKit-navbar-item {
      flex: 1;
      text-align: center;
      cursor: none;
    }
  }
  .TUIKit-main-container {
    flex: 1;
    .TUIKit-main {
      .TUIKit-main-aside {
        flex: 1;
      }
      .TUIKit-main-main {
        .chat-popup {
          position: absolute;
          max-width: 100%;
          max-height: 100%;
        }
      }
    }
    .callkit-container {
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
  }
}
